package cn.jbolt.mail.test;

import java.sql.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 深度邮件分析工具 - 找出为什么内联附件没有下载
 */
public class DeepEmailAnalysis {
    
    private static final String JDBC_URL = "********************************************************************************************************";
    private static final String USERNAME = "stonecrm";
    private static final String PASSWORD = "Stonecrm@2025";
    
    private static Connection getConnection() throws SQLException {
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
        } catch (ClassNotFoundException e) {
            throw new SQLException("MySQL驱动未找到", e);
        }
        return DriverManager.getConnection(JDBC_URL, USERNAME, PASSWORD);
    }
    
    public static void deepAnalyzeEmail(Long emailId) {
        System.out.println("🔬 === 深度分析邮件解析问题 ===");
        System.out.println("邮件ID: " + emailId);
        
        Connection conn = null;
        try {
            conn = getConnection();
            
            // 1. 检查邮件是否真的被重新处理过
            checkEmailProcessingHistory(conn, emailId);
            
            // 2. 检查邮件解析日志
            checkEmailParsingLogs(conn, emailId);
            
            // 3. 检查邮件内容的详细信息
            analyzeEmailContentDetails(conn, emailId);
            
            // 4. 检查邮箱账号的处理状态
            checkEmailAccountStatus(conn, emailId);
            
            // 5. 检查是否有其他邮件成功处理了内联附件
            checkOtherEmailsWithInlineAttachments(conn, emailId);
            
            // 6. 分析可能的代码执行路径问题
            analyzeCodeExecutionPath(conn, emailId);
            
        } catch (Exception e) {
            System.out.println("❌ 深度分析失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            if (conn != null) {
                try { conn.close(); } catch (SQLException e) {}
            }
        }
    }
    
    private static void checkEmailProcessingHistory(Connection conn, Long emailId) throws SQLException {
        System.out.println("\n📊 1. 检查邮件处理历史");
        
        String sql = "SELECT created_at, updated_at FROM email_messages WHERE id = ?";
        PreparedStatement stmt = conn.prepareStatement(sql);
        stmt.setLong(1, emailId);
        ResultSet rs = stmt.executeQuery();
        
        if (rs.next()) {
            Timestamp createdAt = rs.getTimestamp("created_at");
            Timestamp updatedAt = rs.getTimestamp("updated_at");
            
            System.out.println("  - 邮件创建时间: " + createdAt);
            System.out.println("  - 邮件更新时间: " + updatedAt);
            
            if (createdAt.equals(updatedAt)) {
                System.out.println("  ⚠️  邮件从未被更新过，可能没有重新处理");
            } else {
                System.out.println("  ✅ 邮件已被更新，可能已重新处理");
            }
        }
        
        rs.close();
        stmt.close();
    }
    
    private static void checkEmailParsingLogs(Connection conn, Long emailId) throws SQLException {
        System.out.println("\n📝 2. 检查邮件解析日志");
        
        // 检查是否有日志表
        String[] logTables = {
            "email_parsing_log", 
            "email_process_log", 
            "system_log", 
            "application_log"
        };
        
        for (String tableName : logTables) {
            try {
                String checkSql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'stonecrm' AND table_name = ?";
                PreparedStatement checkStmt = conn.prepareStatement(checkSql);
                checkStmt.setString(1, tableName);
                ResultSet checkRs = checkStmt.executeQuery();
                
                if (checkRs.next() && checkRs.getInt(1) > 0) {
                    System.out.println("  ✅ 找到日志表: " + tableName);
                    
                    // 查询相关日志
                    String logSql = "SELECT * FROM " + tableName + " WHERE message LIKE ? OR content LIKE ? ORDER BY created_at DESC LIMIT 5";
                    PreparedStatement logStmt = conn.prepareStatement(logSql);
                    logStmt.setString(1, "%" + emailId + "%");
                    logStmt.setString(2, "%" + emailId + "%");
                    ResultSet logRs = logStmt.executeQuery();
                    
                    while (logRs.next()) {
                        System.out.println("    - " + logRs.getTimestamp("created_at") + ": " + 
                            logRs.getString("message"));
                    }
                    
                    logRs.close();
                    logStmt.close();
                } else {
                    System.out.println("  ❌ 未找到日志表: " + tableName);
                }
                
                checkRs.close();
                checkStmt.close();
                
            } catch (SQLException e) {
                System.out.println("  ⚠️  检查日志表 " + tableName + " 失败: " + e.getMessage());
            }
        }
    }
    
    private static void analyzeEmailContentDetails(Connection conn, Long emailId) throws SQLException {
        System.out.println("\n🔍 3. 分析邮件内容详情");
        
        String sql = "SELECT content_html, content_text FROM email_messages WHERE id = ?";
        PreparedStatement stmt = conn.prepareStatement(sql);
        stmt.setLong(1, emailId);
        ResultSet rs = stmt.executeQuery();
        
        if (rs.next()) {
            String htmlContent = rs.getString("content_html");
            String textContent = rs.getString("content_text");
            
            System.out.println("  - HTML内容长度: " + (htmlContent != null ? htmlContent.length() : 0));
            System.out.println("  - 文本内容长度: " + (textContent != null ? textContent.length() : 0));
            
            if (htmlContent != null) {
                // 详细分析CID引用
                Pattern cidPattern = Pattern.compile("cid:([^\\s\"']+)", Pattern.CASE_INSENSITIVE);
                Matcher matcher = cidPattern.matcher(htmlContent);
                
                System.out.println("  - CID引用详细分析:");
                int count = 0;
                while (matcher.find()) {
                    count++;
                    String cid = matcher.group(1);
                    System.out.println("    " + count + ". CID: " + cid);
                    
                    // 检查CID的上下文
                    int start = Math.max(0, matcher.start() - 100);
                    int end = Math.min(htmlContent.length(), matcher.end() + 100);
                    String context = htmlContent.substring(start, end);
                    System.out.println("       上下文: " + context.replaceAll("\\s+", " "));
                }
                
                // 检查是否有multipart相关的内容
                if (htmlContent.toLowerCase().contains("multipart")) {
                    System.out.println("  ✅ HTML内容中包含'multipart'关键字");
                } else {
                    System.out.println("  ❌ HTML内容中不包含'multipart'关键字");
                }
            }
        }
        
        rs.close();
        stmt.close();
    }
    
    private static void checkEmailAccountStatus(Connection conn, Long emailId) throws SQLException {
        System.out.println("\n📮 4. 检查邮箱账号状态");
        
        String sql = "SELECT ea.*, em.account_id FROM email_account ea " +
                    "JOIN email_messages em ON ea.id = em.account_id " +
                    "WHERE em.id = ?";
        PreparedStatement stmt = conn.prepareStatement(sql);
        stmt.setLong(1, emailId);
        ResultSet rs = stmt.executeQuery();
        
        if (rs.next()) {
            System.out.println("  - 账号ID: " + rs.getInt("id"));
            System.out.println("  - 用户名: " + rs.getString("username"));
            System.out.println("  - 启用状态: " + rs.getBoolean("enable"));
            System.out.println("  - 有效状态: " + rs.getBoolean("valid"));
            System.out.println("  - IMAP主机: " + rs.getString("imap_host"));
            System.out.println("  - IMAP端口: " + rs.getInt("imap_port"));
            System.out.println("  - 最后同步时间: " + rs.getTimestamp("last_sync_time"));
            
            if (!rs.getBoolean("enable")) {
                System.out.println("  ⚠️  账号未启用！");
            }
            if (!rs.getBoolean("valid")) {
                System.out.println("  ⚠️  账号无效！");
            }
        }
        
        rs.close();
        stmt.close();
    }
    
    private static void checkOtherEmailsWithInlineAttachments(Connection conn, Long emailId) throws SQLException {
        System.out.println("\n📎 5. 检查其他邮件的内联附件处理情况");
        
        // 查找最近成功处理内联附件的邮件
        String sql = "SELECT em.id, em.subject, COUNT(ea.id) as attachment_count, " +
                    "SUM(CASE WHEN ea.cid IS NOT NULL AND ea.cid != '' THEN 1 ELSE 0 END) as inline_count " +
                    "FROM email_messages em " +
                    "JOIN email_attachments ea ON em.id = ea.email_id " +
                    "WHERE ea.cid IS NOT NULL AND ea.cid != '' " +
                    "AND em.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) " +
                    "GROUP BY em.id, em.subject " +
                    "ORDER BY em.created_at DESC LIMIT 5";
        
        PreparedStatement stmt = conn.prepareStatement(sql);
        ResultSet rs = stmt.executeQuery();
        
        boolean foundInlineAttachments = false;
        while (rs.next()) {
            foundInlineAttachments = true;
            System.out.println("  ✅ 邮件 " + rs.getLong("id") + ": " + rs.getString("subject"));
            System.out.println("     内联附件数: " + rs.getInt("inline_count"));
        }
        
        if (!foundInlineAttachments) {
            System.out.println("  ❌ 最近30天内没有任何邮件成功处理内联附件！");
            System.out.println("     这说明可能是系统性问题，不仅仅是这一封邮件的问题");
        }
        
        rs.close();
        stmt.close();
    }
    
    private static void analyzeCodeExecutionPath(Connection conn, Long emailId) throws SQLException {
        System.out.println("\n🔧 6. 分析可能的代码执行路径问题");
        
        // 检查邮件的Content-Type信息（如果存储了的话）
        String sql = "SELECT * FROM email_messages WHERE id = ?";
        PreparedStatement stmt = conn.prepareStatement(sql);
        stmt.setLong(1, emailId);
        ResultSet rs = stmt.executeQuery();
        
        if (rs.next()) {
            String htmlContent = rs.getString("content_html");
            
            System.out.println("  🤔 可能的问题分析:");
            
            // 1. 检查是否是单部分邮件
            if (htmlContent != null && !htmlContent.toLowerCase().contains("multipart")) {
                System.out.println("  ❌ 可能问题1: 邮件可能被识别为单部分邮件");
                System.out.println("     解决方案: 检查EmailParserJakarta中单部分邮件的处理逻辑");
            }
            
            // 2. 检查Content-Type
            System.out.println("  ❌ 可能问题2: 邮件的Content-Type可能不是multipart/*");
            System.out.println("     解决方案: 需要在parseAttachments方法开始处添加详细日志");
            
            // 3. 检查异常处理
            System.out.println("  ❌ 可能问题3: 解析过程中可能有异常被捕获但未记录");
            System.out.println("     解决方案: 增强异常处理和日志记录");
            
            // 4. 检查是否进入了正确的解析分支
            System.out.println("  ❌ 可能问题4: 代码可能没有进入parseAttachmentsFromMultipart方法");
            System.out.println("     解决方案: 在方法入口添加强制日志输出");
        }
        
        rs.close();
        stmt.close();
        
        System.out.println("\n💡 立即行动建议:");
        System.out.println("  1. 在EmailParserJakarta.parseAttachments方法开始处添加强制日志");
        System.out.println("  2. 在parseAttachmentsFromMultipart方法开始处添加强制日志");
        System.out.println("  3. 检查邮件的原始Content-Type是什么");
        System.out.println("  4. 添加更详细的异常捕获和日志记录");
    }
    
    public static void main(String[] args) {
        Long emailId = 1939503822424436736L;
        deepAnalyzeEmail(emailId);
        
        System.out.println("\n🚨 紧急调试建议:");
        System.out.println("1. 立即在EmailParserJakarta中添加System.out.println调试语句");
        System.out.println("2. 重新处理邮件并查看控制台输出");
        System.out.println("3. 确认代码是否真的执行到了我们修改的部分");
    }
}
