package cn.jbolt.admin.emailaccount;

import cn.jbolt._admin.permission.PermissionKey;
import cn.jbolt.admin.emails.EmailsService;
import cn.jbolt.common.config.JBoltUploadFolder;
import cn.jbolt.common.model.EmailAccount;
import cn.jbolt.common.model.EmailFetchTask;
import cn.jbolt.common.model.EmailMessages;
import cn.jbolt.core.base.JBoltMsg;
import cn.jbolt.core.bean.Option;
import cn.jbolt.core.controller.base.JBoltBaseController;
import cn.jbolt.core.kit.JBoltUserKit;
import cn.jbolt.core.permission.CheckPermission;
import cn.jbolt.core.permission.JBoltAdminAuthInterceptor;
import cn.jbolt.core.permission.UnCheck;
import cn.jbolt.core.permission.UnCheckIfSystemAdmin;
import cn.jbolt.mail.MailConfigurationChecker;
import cn.jbolt.mail.gpt.client.EmailClient;
import cn.jbolt.mail.gpt.client.EmailClientPool;
import com.jfinal.aop.Before;
import com.jfinal.aop.Inject;
import com.jfinal.core.Path;
import com.jfinal.core.paragetter.Para;
import com.jfinal.kit.Kv;
import com.jfinal.kit.LogKit;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import com.jfinal.plugin.activerecord.tx.Tx;
import com.jfinal.upload.UploadFile;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 邮箱账户管理
 *
 * @ClassName: EmailAccountAdminController
 * @author: 总管理
 * @date: 2024-05-15 10:34
 */
@CheckPermission(PermissionKey.ADMIN_EMAILACCOUNT)
@UnCheckIfSystemAdmin
@Before(JBoltAdminAuthInterceptor.class)
@Path(value = "/admin/emailAccount", viewPath = "/_view/admin/emailaccount")
public class EmailAccountAdminController extends JBoltBaseController {

    @Inject
    private EmailAccountService service;

    @Inject
    private EmailsService emailsService;

    // 存储运行中的任务
    private static final Map<Integer, EmailClient> runningTasks = new ConcurrentHashMap<>();

    /**
     * 首页
     */
    public void index() {
        render("index.html");
    }

    /**
     * 数据源
     */
    public void datas() {
        renderJsonData(service.getAdminDatas(JBoltUserKit.getEmails(), getKeywords(), getSortColumn("sort_rank"), getSortType("asc"), getBoolean("enable")));
    }

    /**
     * 新增
     */
    public void add() {
        render("add.html");
    }

    /**
     * 保存
     */
    @Before(Tx.class)
    public void save(@Para("emailAccount") EmailAccount emailAccount) {
        renderJson(service.save(emailAccount));
    }

    /**
     * 编辑
     */
    public void edit() {
        EmailAccount emailAccount = service.findById(getInt(0));
        if (emailAccount == null) {
            renderFail(JBoltMsg.DATA_NOT_EXIST);
            return;
        }
        set("emailAccount", emailAccount);
        render("edit.html");
    }

    /**
     * 更新
     */
    @Before(Tx.class)
    public void update(@Para("emailAccount") EmailAccount emailAccount) {
        renderJson(service.update(emailAccount));
    }

    /**
     * 删除
     */
    @Before(Tx.class)
    public void delete() {
        renderJson(service.deleteById(getInt(0)));
    }

    /**
     * 排序 上移
     */
    @Before(Tx.class)
    public void up() {
        renderJson(service.up(getInt(0)));
    }

    /**
     * 排序 下移
     */
    @Before(Tx.class)
    public void down() {
        renderJson(service.down(getInt(0)));
    }

    /**
     * 排序 初始化
     */
    @Before(Tx.class)
    public void initSortRank() {
        renderJson(service.initSortRank());
    }

    /**
     * 进入import_excel.html
     */
    public void initImportExcel() {
        render("import_excel.html");
    }

    /**
     * 下载导入模板
     */
    public void downloadTpl() {
        renderBytesToExcelXlsFile(service.getImportExcelTpl().setFileName("邮箱账户导入模板"));
    }

    /**
     * 执行导入excel
     */
    public void importExcel() {
        String uploadPath = JBoltUploadFolder.todayFolder(JBoltUploadFolder.IMPORT_EXCEL_TEMP_FOLDER);
        UploadFile file = getFile("file", uploadPath);
        if (notExcel(file)) {
            renderJsonFail("请上传excel文件");
            return;
        }
        renderJson(service.importExcel(file.getFile()));
    }

    /**
     * 执行导出excel 根据查询form表单
     */
    public void exportExcelByForm() {
        List<EmailAccount> emailAccountList = service.getAdminDatas(JBoltUserKit.getEmails(), getKeywords(), getSortColumn("sort_rank"), getSortType("asc"), getBoolean("enable"));
        if (notOk(emailAccountList)) {
            renderJsonFail("无有效数据导出");
            return;
        }
        renderBytesToExcelXlsxFile(service.exportExcel(emailAccountList).setFileName("邮箱账户"));
    }

    /**
     * 执行导出excel 根据表格选中数据
     */
    public void exportExcelByCheckedIds() {
        String ids = get("ids");
        if (notOk(ids)) {
            renderJsonFail("未选择有效数据，无法导出");
            return;
        }
        List<EmailAccount> datas = service.getListByIds(ids);
        if (notOk(datas)) {
            renderJsonFail("无有效数据导出");
            return;
        }
        renderBytesToExcelXlsxFile(service.exportExcel(datas).setFileName("邮箱账户"));
    }

    /**
     * 执行导出excel 所有数据
     */
    public void exportExcelAll() {
        List<EmailAccount> datas = service.findAll();
        if (notOk(datas)) {
            renderJsonFail("无有效数据导出");
            return;
        }
        renderBytesToExcelXlsxFile(service.exportExcel(datas).setFileName("邮箱账户"));
    }

    /**
     * 切换启用状态
     */
    @Before(Tx.class)
    public void toggleEnable() {
        renderJson(service.toggleEnable(getInt(0)));
    }

    @Before(Tx.class)
    public void toggleReceive() {
        renderJson(service.toggleBoolean(getInt(0), "receive"));
    }

    @Before(Tx.class)
    public void toggleSend() {
        renderJson(service.toggleBoolean(getInt(0), "send"));
    }

    @Before(Tx.class)
    public void toggleMonitor() {
        int id = getInt(0);
        EmailAccount emailAccount = service.findById(id);
        if (emailAccount == null) {
            renderJsonFail("邮箱账户不存在");
            return;
        }

        // 检查邮箱配置是否有效
        if (!emailAccount.getValid()) {
            renderJsonFail("邮箱配置无效，请先检查配置");
            return;
        }

        // 切换监控状态
        boolean newMonitorStatus = !emailAccount.getMonitor();
        emailAccount.setMonitor(newMonitorStatus);

        // 更新数据库
        Ret ret = service.update(emailAccount);
        if (!ret.isOk()) {
            renderJson(ret);
            return;
        }

        try {
            renderJson(ret.set("msg", newMonitorStatus ? "已开启监控" : "已关闭监控"));
        } catch (Exception e) {
            LogKit.error("Failed to " + (newMonitorStatus ? "add" : "remove") + " email monitor for: " + emailAccount.getUsername(), e);
            // 发生错误时回滚监控状态
            emailAccount.setMonitor(!newMonitorStatus);
            service.update(emailAccount);
            renderJsonFail("操作失败：" + e.getMessage());
        }
    }

    @Before(Tx.class)
    public void toggleBoolean() {
        renderJson(service.toggleBoolean(getInt(0), "valid"));
    }

    /**
     * autocomplete组件专用数据
     */
    @UnCheck
    public void autocompleteDatas() {
        renderJsonData(service.getAutocompleteDatas(get("q", "")));
    }

    /**
     * 得到select radio checkbox专用options数据
     */
    @UnCheck
    public void options() {
        String type = get("type");
        String sql = null;
        if (StringUtils.isEmpty(type)) {
            sql = "select username text, id value from email_account order by username";
        } else if ("receive".equalsIgnoreCase(type)) {
            sql = "select username text, id value from email_account where receive=1 order by username";
        } else if ("send".equalsIgnoreCase(type)) {
            sql = "select username text, id value from email_account where send=1 order by username";
        }
        renderJsonData(service.toOptions(Db.find(sql)));
    }

    @UnCheck
    public void userOptions() {
        List<Option> userOptions = service.getUserOptions();
        renderJsonData(userOptions);
    }

    /**
     * 获取邮箱账户的发件人名称
     */
    @UnCheck
    public void getSenderName() {
        Integer id = getInt("id");
        if (id == null) {
            renderJson(Ret.fail("参数错误"));
            return;
        }

        EmailAccount emailAccount = service.findById(id);
        if (emailAccount == null) {
            renderJson(Ret.fail("邮箱账户不存在"));
            return;
        }

        Kv data = Kv.create()
            .set("senderName", emailAccount.getSenderName())
            .set("nickname", emailAccount.getNickname())
            .set("username", emailAccount.getUsername());

        renderJsonData(data);
    }

    @UnCheck
    public void checkEmailAccount() {
        EmailAccount emailAccount = service.findById(getInt(0));
        if (notOk(emailAccount)) {
            renderJsonFail("邮箱账户不存在");
            return;
        }
        boolean checkIMAPConfiguration = MailConfigurationChecker.checkIMAPConfiguration(emailAccount.getImapHost(), emailAccount.getUsername(), emailAccount.getPassword());
        emailAccount.setValid(checkIMAPConfiguration);
        service.update(emailAccount);
        renderJson(Ret.ok(checkIMAPConfiguration ? "邮箱账户配置正确" : "邮箱账户配置错误").set("checkIMAPConfiguration", checkIMAPConfiguration));
    }

    @UnCheck
    public void checkAllAccount() {
        String emails = JBoltUserKit.getEmails();
        String[] emailSplit = emails.split(",");
        for (String email : emailSplit) {
            EmailAccount emailAccount = service.findById(Integer.parseInt(email));
            boolean checkIMAPConfiguration = MailConfigurationChecker.checkIMAPConfiguration(emailAccount.getImapHost(), emailAccount.getUsername(), emailAccount.getPassword());
            emailAccount.setValid(checkIMAPConfiguration);
            service.update(emailAccount);
        }
        renderJson(Ret.ok("邮箱账户配置检测完成!"));
    }

    /**
     * 进入邮箱消息管理界面
     */
    public void message() {
        Long id = getLong(0);
        String email = get(1);
        EmailAccount account = null;
        if (id != null && id != 0) {
            account = service.findById(id);
        } else if (!StringUtils.isEmpty(email)) {
            account = service.findByEmail(email);
        }

        if (account == null) {
            renderDialogFail(JBoltMsg.DATA_NOT_EXIST);
            return;
        }

        set("account", account);
        render("message.html");
    }

}
