package cn.jbolt.admin.emailaccount;

import cn.jbolt.admin.emails.EmailNameCache;
import cn.jbolt.common.model.EmailAccount;
import cn.jbolt.core.base.JBoltMsg;
import cn.jbolt.core.bean.Option;
import cn.jbolt.core.bean.OptionBean;
import cn.jbolt.core.db.sql.Sql;
import cn.jbolt.core.kit.JBoltUserKit;
import cn.jbolt.core.model.User;
import cn.jbolt.core.poi.excel.JBoltExcel;
import cn.jbolt.core.poi.excel.JBoltExcelHeader;
import cn.jbolt.core.poi.excel.JBoltExcelSheet;
import cn.jbolt.core.poi.excel.JBoltExcelUtil;
import cn.jbolt.core.service.base.JBoltBaseService;
import cn.jbolt.extend.systemlog.ProjectSystemLogTargetType;
import com.google.common.collect.Sets;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Kv;
import com.jfinal.kit.Okv;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.IAtom;
import com.jfinal.plugin.activerecord.Record;
import org.apache.commons.lang.StringUtils;

import java.io.File;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.HashMap;

/**
 * 邮箱账户管理
 *
 * @ClassName: EmailAccountService
 * @author: 总管理
 * @date: 2024-05-15 10:34
 */
public class EmailAccountService extends JBoltBaseService<EmailAccount> {
    private final EmailAccount dao = new EmailAccount().dao();

    @Inject
    private EmailNameCache emailNameCache;

    @Override
    protected EmailAccount dao() {
        return dao;
    }

    @Override
    protected int systemLogTargetType() {
        return ProjectSystemLogTargetType.NONE.getValue();
    }

    /**
     * 后台管理数据查询
     *
     * @param keywords   关键词
     * @param sortColumn 排序列名
     * @param sortType   排序方式 asc desc
     * @param enable     启用/禁用
     * @return
     */
    public List<EmailAccount> getAdminDatas(String emails, String keywords, String sortColumn, String sortType, Boolean enable) {
        //创建sql对象
        Sql sql = selectSql();
        //sql条件处理
        sql.eqBooleanToChar("enable", enable);
        sql.in("id", (Object[]) emails.split(","));
        //关键词模糊查询
        sql.likeMulti(keywords, "username", "imap_host", "smtp_host", "remark");
        //排序
        sql.orderBy(sortColumn, sortType);
        System.out.println(sql.toSql());
        return find(sql);
    }

    /**
     * 保存
     *
     * @param emailAccount
     * @return
     */
    public Ret save(EmailAccount emailAccount) {
        if (emailAccount == null || isOk(emailAccount.getId())) {
            return fail(JBoltMsg.PARAM_ERROR);
        }
        emailAccount.setSortRank(getNextSortRank());
        boolean success = emailAccount.save();
        User user = JBoltUserKit.getUser();
        String emails = user.getEmails();
        if (StringUtils.isEmpty(emails)) {
            user.setEmails(String.valueOf(emailAccount.getId()));
        } else {
            Set<String> emailIdSet = Sets.newLinkedHashSet(List.of(emails.split(",")));
            emailIdSet.add(String.valueOf(emailAccount.getId()));
            user.setEmails(StringUtils.join(emailIdSet, ","));
        }
        user.update();
        if (success) {
            afterSaveOrUpdate(emailAccount);
            //添加日志
            //addSaveSystemLog(emailAccount.getId(), JBoltUserKit.getUserId(), emailAccount.getName());
        }
        return ret(success);
    }

    /**
     * 更新
     *
     * @param emailAccount
     * @return
     */
    public Ret update(EmailAccount emailAccount) {
        if (emailAccount == null || notOk(emailAccount.getId())) {
            return fail(JBoltMsg.PARAM_ERROR);
        }
        //更新时需要判断数据存在
        EmailAccount dbEmailAccount = findById(emailAccount.getId());
        if (dbEmailAccount == null) {
            return fail(JBoltMsg.DATA_NOT_EXIST);
        }
        boolean success = emailAccount.update();
        if (success) {
            afterSaveOrUpdate(emailAccount);
            //添加日志
            //addUpdateSystemLog(emailAccount.getId(), JBoltUserKit.getUserId(), emailAccount.getName());
        }
        return ret(success);
    }

    /**
     * 删除数据后执行的回调
     *
     * @param emailAccount 要删除的model
     * @param kv           携带额外参数一般用不上
     * @return
     */
    @Override
    protected String afterDelete(EmailAccount emailAccount, Kv kv) {
        //addDeleteSystemLog(emailAccount.getId(), JBoltUserKit.getUserId(),emailAccount.getName());
        if (emailAccount != null && emailAccount.getUsername() != null) {
            emailNameCache.removeDisplayName(emailAccount.getUsername());
        }
        return null;
    }

    /**
     * 检测是否可以删除
     *
     * @param emailAccount model
     * @param kv           携带额外参数一般用不上
     * @return
     */
    @Override
    public String checkInUse(EmailAccount emailAccount, Kv kv) {
        //这里用来覆盖 检测是否被其它表引用
        return null;
    }

    /**
     * 上移
     *
     * @param id
     * @return
     */
    public Ret up(Integer id) {
        EmailAccount emailAccount = findById(id);
        if (emailAccount == null) {
            return fail("数据不存在或已被删除");
        }
        Integer rank = emailAccount.getSortRank();
        if (rank == null || rank <= 0) {
            return fail("顺序需要初始化");
        }
        if (rank == 1) {
            return fail("已经是第一个");
        }
        EmailAccount upEmailAccount = findFirst(Okv.by("sort_rank", rank - 1));
        if (upEmailAccount == null) {
            return fail("顺序需要初始化");
        }
        upEmailAccount.setSortRank(rank);
        emailAccount.setSortRank(rank - 1);

        upEmailAccount.update();
        emailAccount.update();
        return SUCCESS;
    }

    /**
     * 下移
     *
     * @param id
     * @return
     */
    public Ret down(Integer id) {
        EmailAccount emailAccount = findById(id);
        if (emailAccount == null) {
            return fail("数据不存在或已被删除");
        }
        Integer rank = emailAccount.getSortRank();
        if (rank == null || rank <= 0) {
            return fail("顺序需要初始化");
        }
        int max = getCount();
        if (rank == max) {
            return fail("已经是最后已一个");
        }
        EmailAccount upEmailAccount = findFirst(Okv.by("sort_rank", rank + 1));
        if (upEmailAccount == null) {
            return fail("顺序需要初始化");
        }
        upEmailAccount.setSortRank(rank);
        emailAccount.setSortRank(rank + 1);

        upEmailAccount.update();
        emailAccount.update();
        return SUCCESS;
    }

    /**
     * 初始化排序
     */
    public Ret initSortRank() {
        List<EmailAccount> allList = findAll();
        if (!allList.isEmpty()) {
            for (int i = 0; i < allList.size(); i++) {
                allList.get(i).setSortRank(i + 1);
            }
            batchUpdate(allList);
        }
        //添加日志
        //addUpdateSystemLog(null, JBoltUserKit.getUserId(), "所有数据", "的顺序:初始化所有");
        return SUCCESS;
    }

    /**
     * 生成excel导入使用的模板
     *
     * @return
     */
    public JBoltExcel getImportExcelTpl() {
        return JBoltExcel
                //创建
                .create()
                .setSheets(
                        JBoltExcelSheet.create()
                                //设置列映射 顺序 标题名称 不处理别名
                                .setHeaders(1, false,
                                        JBoltExcelHeader.create("用户名", 15),
                                        JBoltExcelHeader.create("密码", 15),
                                        JBoltExcelHeader.create("imap地址", 15),
                                        JBoltExcelHeader.create("imap端口", 15),
                                        JBoltExcelHeader.create("smtp地址", 15),
                                        JBoltExcelHeader.create("smtp端口", 15)
                                )
                );
    }

    /**
     * 读取excel文件
     *
     * @param file
     * @return
     */
    public Ret importExcel(File file) {
        StringBuilder errorMsg = new StringBuilder();
        JBoltExcel jBoltExcel = JBoltExcel
                //从excel文件创建JBoltExcel实例
                .from(file)
                //设置工作表信息
                .setSheets(
                        JBoltExcelSheet.create()
                                //设置列映射 顺序 标题名称
                                .setHeaders(1,
                                        JBoltExcelHeader.create("username", "用户名"),
                                        JBoltExcelHeader.create("password", "密码"),
                                        JBoltExcelHeader.create("imap_host", "imap地址"),
                                        JBoltExcelHeader.create("imap_port", "imap端口"),
                                        JBoltExcelHeader.create("smtp_host", "smtp地址"),
                                        JBoltExcelHeader.create("smtp_port", "smtp端口")
                                )
                                //从第三行开始读取
                                .setDataStartRow(2)
                );
        //从指定的sheet工作表里读取数据
        List<EmailAccount> emailAccounts = JBoltExcelUtil.readModels(jBoltExcel, 1, EmailAccount.class, errorMsg);
        if (notOk(emailAccounts)) {
            if (errorMsg.length() > 0) {
                return fail(errorMsg.toString());
            } else {
                return fail(JBoltMsg.DATA_IMPORT_FAIL_EMPTY);
            }
        }
        //执行批量操作
        boolean success = tx(new IAtom() {
            @Override
            public boolean run() throws SQLException {
                batchSave(emailAccounts);
                return true;
            }
        });

        if (!success) {
            return fail(JBoltMsg.DATA_IMPORT_FAIL);
        }
        return SUCCESS;
    }

    /**
     * 生成要导出的Excel
     *
     * @return
     */
    public JBoltExcel exportExcel(List<EmailAccount> datas) {
        return JBoltExcel
                //创建
                .create()
                //设置工作表
                .setSheets(
                        //设置工作表 列映射 顺序 标题名称
                        JBoltExcelSheet
                                .create()
                                //表头映射关系
                                .setHeaders(1,
                                        JBoltExcelHeader.create("username", "用户名", 15),
                                        JBoltExcelHeader.create("password", "密码", 15),
                                        JBoltExcelHeader.create("imap_host", "imap地址", 15),
                                        JBoltExcelHeader.create("imap_port", "imap端口", 15),
                                        JBoltExcelHeader.create("smtp_host", "smtp地址", 15),
                                        JBoltExcelHeader.create("smtp_port", "smtp端口", 15)
                                )
                                //设置导出的数据源 来自于数据库查询出来的Model List
                                .setModelDatas(2, datas)
                );
    }

    /**
     * toggle操作执行后的回调处理
     */
    @Override
    protected String afterToggleBoolean(EmailAccount emailAccount, String column, Kv kv) {
        //addUpdateSystemLog(emailAccount.getId(), JBoltUserKit.getUserId(), emailAccount.getName(),"的字段["+column+"]值:"+emailAccount.get(column));
        /**
         switch(column){
         case "enable":
         break;
         }
         */
        return null;
    }

    public List<EmailAccount> getAutocompleteDatas(String q) {
        return dao.find("select distinct imap_host,imap_port,smtp_host,smtp_port from email_account where imap_host like ? or smtp_host like ? limit 5", "%" + q + "%", "%" + q + "%");
    }

    public List<EmailAccount> getDatas() {
        return findAll();
    }

    protected String afterSaveOrUpdate(EmailAccount emailAccount) {
        if (emailAccount != null && emailAccount.getUsername() != null) {
            String nickname = emailAccount.getNickname();
            if (nickname != null) {
                emailNameCache.updateDisplayName(emailAccount.getUsername(), nickname);
            }else{
                emailNameCache.removeDisplayName(emailAccount.getUsername());
            }
        }
        return null;
    }

    public EmailAccount findByEmail(String fromEmail) {
        return dao.findFirst("select * from email_account where username=?", fromEmail);
    }


    public List<EmailAccount> findAllEnable() {
        return dao.find("select * from email_account where enable=?", true);
    }

    public List<Option> getUserOptions() {
        String sendEmails = JBoltUserKit.getUser().getSendEmails();
        List<Option> options = new ArrayList<>();
        if (StringUtils.isEmpty(sendEmails)) {
            return options;
        }
        List<Record> records = Db.find("select id value, COALESCE(sender_name, nickname, username) text, sender_name, username from email_account where id in (" + sendEmails + ")");

        // 转换为包含额外数据的Option对象
        for (Record record : records) {
            OptionBean option = new OptionBean();
            option.setValue(record.get("value"));
            option.setText(record.getStr("text"));
            // 这里我们需要一个能携带额外数据的Option实现
            options.add(option);
        }
        return options;
    }

    /**
     * 查找所有启用的邮箱账号
     *
     * @return 启用的邮箱账号列表
     */
    public List<EmailAccount> findAllEnabled() {
        return dao.find(
                "SELECT * FROM email_account WHERE enable = ? ORDER BY id",
                EmailAccount.STATUS_ENABLED);
    }


    /**
     * 启用邮箱账号
     *
     * @param id 账号ID
     * @return 是否成功
     */
    public boolean enable(Long id) {
        EmailAccount account = dao.findById(id);
        if (account != null) {
            account.setEnable(true);
            account.setUpdateTime(new Date());
            return account.update();
        }
        return false;
    }

    /**
     * 禁用邮箱账号
     *
     * @param id 账号ID
     * @return 是否成功
     */
    public boolean disable(Long id) {
        EmailAccount account = dao.findById(id);
        if (account != null) {
            account.setEnable(false);
            account.setUpdateTime(new Date());
            return account.update();
        }
        return false;
    }

    /**
     * 删除邮箱账号
     *
     * @param id 账号ID
     * @return 是否成功
     */
    public boolean delete(Long id) {
        return dao.deleteById(id);
    }

    /**
     * 切换接收模式
     *
     * @param id   账号ID
     * @param mode 接收模式
     * @return 是否成功
     */
    public boolean switchReceiveMode(Long id, int mode) {
        EmailAccount account = dao.findById(id);
        if (account != null) {
            account.setReceiveMode(mode);
            account.setUpdateTime(new Date());
            return account.update();
        }
        return false;
    }

    /**
     * 设置轮询间隔
     *
     * @param id       账号ID
     * @param interval 间隔秒数
     * @return 是否成功
     */
    public boolean setPollingInterval(Long id, int interval) {
        if (interval < 10) {
            interval = 10; // 最小10秒
        }

        EmailAccount account = dao.findById(id);
        if (account != null) {
            account.setPollingInterval(interval);
            account.setUpdateTime(new Date());
            return account.update();
        }
        return false;
    }

    /**
     * 重置首次同步状态
     *
     * @param id 账号ID
     * @return 是否成功
     */
    public boolean resetFirstSync(Long id) {
        EmailAccount account = dao.findById(id);
        if (account != null) {
            account.setFirstSyncCompleted(false);
            account.setLastSyncTime(new Date());
            account.setUpdateTime(new Date());
            return account.update();
        }
        return false;
    }

    public void enableAccount(String email) {
        EmailAccount emailAccount = findByEmail(email);
        emailAccount.setEnable(true);
        emailAccount.setUpdateTime(new Date());
        emailAccount.update();
    }

    public void disableAccount(String email) {
        EmailAccount emailAccount = findByEmail(email);
        emailAccount.setEnable(false);
        emailAccount.setUpdateTime(new Date());
        emailAccount.update();
    }

    /**
     * 获取用户的邮箱账户树结构
     *
     * @param userId 用户ID
     * @return 邮箱账户树结构
     */
    public List<Record> getEmailAccountTree(Long userId) {
        if (userId == null) {
            return new ArrayList<>();
        }

        // 获取用户邮箱账户
        List<Record> accounts = Db.find("SELECT id, nickname name, username email,'root' type FROM email_account WHERE id in (select email_account_id from user_email where user_id=?)", userId);
        
        List<Record> result = new ArrayList<>();
        
        // 创建根节点
        Record root = new Record();
        root.set("id", "root");
        root.set("text", "所有邮箱");
        root.set("icon", "fa fa-envelope");
        
        Map<String, Object> state = new HashMap<>();
        state.put("opened", true);
        root.set("state", state);
        
        Map<String, Object> data = new HashMap<>();
        data.put("type", "root");
        root.set("data", data);
        
        // 添加子节点
        List<Record> children = new ArrayList<>();
        for (Record account : accounts) {
            Record node = new Record();
            node.set("id", account.getInt("id"));
            node.set("text", account.getStr("name") + " (" + account.getStr("email") + ")");
            node.set("icon", "fa fa-envelope-o");
            
            Map<String, Object> nodeData = new HashMap<>();
            nodeData.put("type", account.getStr("type"));
            node.set("data", nodeData);
            
            children.add(node);
        }
        
        root.set("children", children);
        result.add(root);
        
        return result;
    }

    /**
     * 获取用户的邮箱账户列表
     *
     * @param userId 用户ID
     * @return 邮箱账户列表
     */
    public List<Record> getEmailAccountsByUserId(Long userId) {
        if (userId == null) {
            return new ArrayList<>();
        }
        
        return Db.find("SELECT id, name, email, type FROM email_account WHERE user_id = ?", userId);
    }
}