-- 为LLM提供商表添加代理配置字段

-- 1. 检查当前llm_provider表结构
DESCRIBE llm_provider;

-- 2. 添加代理配置字段
ALTER TABLE llm_provider 
ADD COLUMN use_proxy TINYINT(1) DEFAULT 0 COMMENT '是否使用代理 0-不使用 1-使用',
ADD COLUMN proxy_host VARCHAR(255) DEFAULT '' COMMENT '代理主机地址',
ADD COLUMN proxy_port INT DEFAULT 0 COMMENT '代理端口',
ADD COLUMN proxy_type VARCHAR(20) DEFAULT 'HTTP' COMMENT '代理类型 HTTP/SOCKS5';

-- 3. 为国外模型设置代理配置
-- Gemini需要代理
UPDATE llm_provider 
SET 
    use_proxy = 1,
    proxy_host = '127.0.0.1',
    proxy_port = 7890,
    proxy_type = 'HTTP'
WHERE name = 'gemini';

-- OpenAI需要代理（如果存在）
UPDATE llm_provider 
SET 
    use_proxy = 1,
    proxy_host = '127.0.0.1',
    proxy_port = 7890,
    proxy_type = 'HTTP'
WHERE name = 'openai';

-- Claude需要代理（如果存在）
UPDATE llm_provider 
SET 
    use_proxy = 1,
    proxy_host = '127.0.0.1',
    proxy_port = 7890,
    proxy_type = 'HTTP'
WHERE name = 'claude';

-- 4. 国内模型不需要代理
-- Kimi Moonshot不需要代理
UPDATE llm_provider 
SET 
    use_proxy = 0,
    proxy_host = '',
    proxy_port = 0,
    proxy_type = 'HTTP'
WHERE name IN ('Kimi Moonshot', 'kimi', 'moonshot');

-- 通义千问不需要代理
UPDATE llm_provider 
SET 
    use_proxy = 0,
    proxy_host = '',
    proxy_port = 0,
    proxy_type = 'HTTP'
WHERE name IN ('qwen', 'tongyi', '通义千问');

-- 文心一言不需要代理
UPDATE llm_provider 
SET 
    use_proxy = 0,
    proxy_host = '',
    proxy_port = 0,
    proxy_type = 'HTTP'
WHERE name IN ('ernie', 'wenxin', '文心一言');

-- 智谱AI不需要代理
UPDATE llm_provider 
SET 
    use_proxy = 0,
    proxy_host = '',
    proxy_port = 0,
    proxy_type = 'HTTP'
WHERE name IN ('zhipu', 'glm', '智谱');

-- 5. 验证配置结果
SELECT 
    id,
    name,
    CASE 
        WHEN use_proxy = 1 THEN '✓ 使用代理'
        ELSE '✗ 不使用代理'
    END as proxy_status,
    CASE 
        WHEN use_proxy = 1 AND proxy_host != '' AND proxy_port > 0 
        THEN CONCAT(proxy_host, ':', proxy_port, ' (', proxy_type, ')')
        ELSE '无代理配置'
    END as proxy_config,
    status,
    api_type
FROM llm_provider 
ORDER BY 
    CASE WHEN use_proxy = 1 THEN 0 ELSE 1 END,  -- 使用代理的排在前面
    name;

-- 6. 检查代理配置统计
SELECT 
    CASE 
        WHEN use_proxy = 1 THEN '需要代理'
        ELSE '不需要代理'
    END as proxy_requirement,
    COUNT(*) as provider_count,
    GROUP_CONCAT(name ORDER BY name) as providers
FROM llm_provider 
WHERE status = 1
GROUP BY use_proxy
ORDER BY use_proxy DESC;

-- 7. 常用代理配置模板
/*
-- Clash代理配置
UPDATE llm_provider SET proxy_host = '127.0.0.1', proxy_port = 7890 WHERE use_proxy = 1;

-- V2Ray代理配置  
UPDATE llm_provider SET proxy_host = '127.0.0.1', proxy_port = 1080 WHERE use_proxy = 1;

-- 自定义代理配置
UPDATE llm_provider SET proxy_host = 'your_proxy_host', proxy_port = your_proxy_port WHERE use_proxy = 1;

-- 禁用所有代理
UPDATE llm_provider SET use_proxy = 0;

-- 启用所有国外模型的代理
UPDATE llm_provider SET use_proxy = 1, proxy_host = '127.0.0.1', proxy_port = 7890 
WHERE name IN ('gemini', 'openai', 'claude');
*/
