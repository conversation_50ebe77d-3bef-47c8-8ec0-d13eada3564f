package cn.jbolt.mail.test;

import java.sql.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 直接使用JDBC的邮件诊断工具
 */
public class DirectDatabaseDiagnostic {
    
    // 数据库连接信息
    private static final String JDBC_URL = "********************************************************************************************************";
    private static final String USERNAME = "stonecrm";
    private static final String PASSWORD = "Stonecrm@2025";
    
    /**
     * 获取数据库连接
     */
    private static Connection getConnection() throws SQLException {
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
        } catch (ClassNotFoundException e) {
            throw new SQLException("MySQL驱动未找到", e);
        }
        return DriverManager.getConnection(JDBC_URL, USERNAME, PASSWORD);
    }
    
    /**
     * 诊断指定邮件的附件问题
     */
    public static void diagnoseEmail(Long emailId) {
        System.out.println("=== 开始诊断邮件附件问题，邮件ID: " + emailId + " ===");
        
        Connection conn = null;
        try {
            conn = getConnection();
            System.out.println("✅ 数据库连接成功");
            
            // 1. 查询邮件基本信息
            String emailSql = "SELECT id, subject, from_address, account_id, uid_value, LENGTH(content_html) as html_length, content_html FROM email_messages WHERE id = ?";
            PreparedStatement emailStmt = conn.prepareStatement(emailSql);
            emailStmt.setLong(1, emailId);
            ResultSet emailRs = emailStmt.executeQuery();
            
            if (!emailRs.next()) {
                System.out.println("❌ 未找到邮件记录: " + emailId);
                return;
            }
            
            System.out.println("\n📧 邮件基本信息:");
            System.out.println("  - 主题: " + emailRs.getString("subject"));
            System.out.println("  - 发件人: " + emailRs.getString("from_address"));
            System.out.println("  - 账号ID: " + emailRs.getInt("account_id"));
            System.out.println("  - UID: " + emailRs.getLong("uid_value"));
            System.out.println("  - HTML长度: " + emailRs.getInt("html_length") + " 字符");
            
            // 2. 分析HTML内容中的CID引用
            String htmlContent = emailRs.getString("content_html");
            if (htmlContent != null && !htmlContent.trim().isEmpty()) {
                System.out.println("\n🔍 HTML内容分析:");
                
                // 查找CID引用
                Pattern cidPattern = Pattern.compile("cid:([^\\s\"']+)", Pattern.CASE_INSENSITIVE);
                Matcher matcher = cidPattern.matcher(htmlContent);
                
                int cidCount = 0;
                while (matcher.find()) {
                    cidCount++;
                    String cid = matcher.group(1);
                    System.out.println("  - 发现CID引用 " + cidCount + ": " + cid);
                    
                    // 检查数据库中是否有对应的附件记录
                    String attachSql = "SELECT id, file_name, status, path, error_message FROM email_attachments WHERE email_id = ? AND cid = ?";
                    PreparedStatement attachStmt = conn.prepareStatement(attachSql);
                    attachStmt.setLong(1, emailId);
                    attachStmt.setString(2, cid);
                    ResultSet attachRs = attachStmt.executeQuery();
                    
                    if (attachRs.next()) {
                        String fileName = attachRs.getString("file_name");
                        int status = attachRs.getInt("status");
                        String path = attachRs.getString("path");
                        String errorMsg = attachRs.getString("error_message");
                        
                        System.out.println("    ✅ 数据库中找到对应附件: " + fileName);
                        System.out.println("    - 状态: " + getStatusText(status));
                        System.out.println("    - 路径: " + path);
                        
                        if (errorMsg != null && !errorMsg.trim().isEmpty()) {
                            System.out.println("    - 错误: " + errorMsg);
                        }
                        
                        // 检查文件是否存在
                        if (path != null) {
                            java.io.File file = new java.io.File(path);
                            System.out.println("    - 文件存在: " + (file.exists() ? "✅ 是" : "❌ 否"));
                            if (file.exists()) {
                                System.out.println("    - 文件大小: " + file.length() + " 字节");
                            }
                        }
                    } else {
                        System.out.println("    ❌ 数据库中未找到对应附件记录！");
                    }
                    attachRs.close();
                    attachStmt.close();
                }
                
                if (cidCount == 0) {
                    System.out.println("  - 未发现CID引用");
                } else {
                    System.out.println("  - 总共发现 " + cidCount + " 个CID引用");
                }
            } else {
                System.out.println("\n⚠️  邮件HTML内容为空");
            }
            
            emailRs.close();
            emailStmt.close();
            
            // 3. 查询所有附件记录
            String allAttachSql = "SELECT id, file_name, cid, status, path, file_size, error_message, created_at FROM email_attachments WHERE email_id = ? ORDER BY created_at";
            PreparedStatement allAttachStmt = conn.prepareStatement(allAttachSql);
            allAttachStmt.setLong(1, emailId);
            ResultSet allAttachRs = allAttachStmt.executeQuery();
            
            System.out.println("\n📎 所有附件记录:");
            
            int totalCount = 0;
            int inlineCount = 0;
            int normalCount = 0;
            int successCount = 0;
            int failedCount = 0;
            
            while (allAttachRs.next()) {
                totalCount++;
                String fileName = allAttachRs.getString("file_name");
                String cid = allAttachRs.getString("cid");
                int status = allAttachRs.getInt("status");
                String path = allAttachRs.getString("path");
                long fileSize = allAttachRs.getLong("file_size");
                String errorMsg = allAttachRs.getString("error_message");
                Timestamp createdAt = allAttachRs.getTimestamp("created_at");
                
                System.out.println("\n  📄 附件 " + totalCount + ": " + fileName);
                System.out.println("    - CID: " + (cid != null && !cid.trim().isEmpty() ? cid : "无"));
                System.out.println("    - 状态: " + getStatusText(status));
                System.out.println("    - 路径: " + path);
                System.out.println("    - 大小: " + fileSize + " 字节");
                System.out.println("    - 创建时间: " + createdAt);
                
                if (cid != null && !cid.trim().isEmpty()) {
                    inlineCount++;
                    System.out.println("    - 类型: 内联附件");
                } else {
                    normalCount++;
                    System.out.println("    - 类型: 普通附件");
                }
                
                if (status == 2) successCount++;
                else if (status == 3) failedCount++;
                
                if (errorMsg != null && !errorMsg.trim().isEmpty()) {
                    System.out.println("    - 错误: " + errorMsg);
                }
                
                // 检查文件是否存在
                if (path != null) {
                    java.io.File file = new java.io.File(path);
                    System.out.println("    - 文件存在: " + (file.exists() ? "✅ 是" : "❌ 否"));
                    if (file.exists()) {
                        System.out.println("    - 实际文件大小: " + file.length() + " 字节");
                    }
                }
            }
            
            allAttachRs.close();
            allAttachStmt.close();
            
            System.out.println("\n📊 统计结果:");
            System.out.println("  - 总附件数: " + totalCount);
            System.out.println("  - 内联附件: " + inlineCount);
            System.out.println("  - 普通附件: " + normalCount);
            System.out.println("  - 成功下载: " + successCount);
            System.out.println("  - 下载失败: " + failedCount);
            
            // 4. 问题诊断和建议
            System.out.println("\n🔧 问题诊断:");
            
            if (htmlContent != null && htmlContent.contains("cid:") && inlineCount == 0) {
                System.out.println("  ❌ 发现问题：HTML中有CID引用但没有内联附件记录");
                System.out.println("\n💡 建议解决方案:");
                System.out.println("  1. 检查EmailParserJakarta的内联附件识别逻辑");
                System.out.println("  2. 查看邮件解析日志，寻找错误信息");
                System.out.println("  3. 重新解析该邮件（删除现有附件记录后重新处理）");
                System.out.println("  4. 验证邮件原始数据中确实包含对应的内联附件");
            } else if (inlineCount > 0 && failedCount > 0) {
                System.out.println("  ⚠️  发现问题：有内联附件但部分下载失败");
                System.out.println("  建议检查失败原因和文件路径");
            } else if (inlineCount > 0) {
                System.out.println("  ✅ 内联附件处理正常");
            } else {
                System.out.println("  ℹ️  该邮件没有内联附件");
            }
            
        } catch (SQLException e) {
            System.out.println("❌ 数据库操作失败: " + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            System.out.println("❌ 诊断过程中出错: " + e.getMessage());
            e.printStackTrace();
        } finally {
            if (conn != null) {
                try {
                    conn.close();
                } catch (SQLException e) {
                    System.out.println("关闭数据库连接失败: " + e.getMessage());
                }
            }
        }
        
        System.out.println("\n=== 诊断完成 ===");
    }
    
    /**
     * 获取状态文本描述
     */
    private static String getStatusText(int status) {
        switch (status) {
            case 0: return "未下载";
            case 1: return "下载中";
            case 2: return "已下载";
            case 3: return "下载失败";
            case 4: return "已跳过";
            case 5: return "待处理";
            default: return "状态" + status;
        }
    }
    
    /**
     * 主方法
     */
    public static void main(String[] args) {
        System.out.println("邮件附件诊断工具（直接数据库版本）");
        System.out.println("================================");
        
        // 诊断指定邮件
        Long emailId = 1939503822424436736L;
        diagnoseEmail(emailId);
    }
}
