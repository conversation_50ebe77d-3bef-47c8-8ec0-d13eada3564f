package cn.jbolt.admin.llm;

import cn.jbolt.common.model.LlmProvider;
import cn.jbolt.core.base.JBoltController;
import com.jfinal.aop.Inject;
import com.jfinal.kit.LogKit;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * LLM提供商代理配置控制器
 */
public class LlmProviderProxyController extends JBoltController {
    
    /**
     * 获取所有提供商的代理配置
     */
    public void getProxyConfigs() {
        try {
            List<LlmProvider> providers = new LlmProvider().dao().find(
                "SELECT * FROM llm_provider WHERE status = 1 ORDER BY priority"
            );
            
            renderJsonData(providers);
            
        } catch (Exception e) {
            LogKit.error("获取提供商代理配置失败: " + e.getMessage());
            renderJsonFail("获取配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新提供商代理配置
     */
    public void updateProxyConfig() {
        try {
            Long providerId = getParaToLong("providerId");
            Boolean useProxy = getParaToBoolean("useProxy", false);
            String proxyHost = getPara("proxyHost", "");
            Integer proxyPort = getParaToInt("proxyPort", 0);
            String proxyType = getPara("proxyType", "HTTP");
            
            if (providerId == null) {
                renderJsonFail("提供商ID不能为空");
                return;
            }
            
            LlmProvider provider = new LlmProvider().dao().findById(providerId);
            if (provider == null) {
                renderJsonFail("提供商不存在");
                return;
            }
            
            // 更新代理配置
            provider.set("use_proxy", useProxy ? 1 : 0);
            provider.set("proxy_host", proxyHost.trim());
            provider.set("proxy_port", proxyPort);
            provider.set("proxy_type", proxyType);
            provider.set("update_time", new java.util.Date());
            
            boolean success = provider.update();
            
            if (success) {
                LogKit.info("更新提供商 " + provider.getName() + " 的代理配置: " + 
                           (useProxy ? proxyHost + ":" + proxyPort : "不使用代理"));
                renderJsonSuccess("代理配置更新成功");
            } else {
                renderJsonFail("代理配置更新失败");
            }
            
        } catch (Exception e) {
            LogKit.error("更新提供商代理配置失败: " + e.getMessage());
            renderJsonFail("更新失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量设置代理配置
     */
    public void batchUpdateProxy() {
        try {
            String[] providerNames = getParaValues("providerNames");
            Boolean useProxy = getParaToBoolean("useProxy", false);
            String proxyHost = getPara("proxyHost", "");
            Integer proxyPort = getParaToInt("proxyPort", 0);
            String proxyType = getPara("proxyType", "HTTP");
            
            if (providerNames == null || providerNames.length == 0) {
                renderJsonFail("请选择要配置的提供商");
                return;
            }
            
            int successCount = 0;
            int totalCount = providerNames.length;
            
            for (String providerName : providerNames) {
                try {
                    LlmProvider provider = new LlmProvider().dao().findFirst(
                        "SELECT * FROM llm_provider WHERE name = ? AND status = 1", providerName
                    );
                    
                    if (provider != null) {
                        provider.set("use_proxy", useProxy ? 1 : 0);
                        provider.set("proxy_host", proxyHost.trim());
                        provider.set("proxy_port", proxyPort);
                        provider.set("proxy_type", proxyType);
                        provider.set("update_time", new java.util.Date());
                        
                        if (provider.update()) {
                            successCount++;
                            LogKit.info("批量更新提供商 " + providerName + " 的代理配置成功");
                        }
                    }
                    
                } catch (Exception e) {
                    LogKit.warn("批量更新提供商 " + providerName + " 的代理配置失败: " + e.getMessage());
                }
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("totalCount", totalCount);
            result.put("successCount", successCount);
            result.put("failCount", totalCount - successCount);
            
            if (successCount == totalCount) {
                renderJsonData(result, "批量配置成功");
            } else if (successCount > 0) {
                renderJsonData(result, "部分配置成功");
            } else {
                renderJsonFail("批量配置失败");
            }
            
        } catch (Exception e) {
            LogKit.error("批量更新代理配置失败: " + e.getMessage());
            renderJsonFail("批量更新失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取代理配置统计
     */
    public void getProxyStats() {
        try {
            // 统计使用代理的提供商
            List<Map<String, Object>> stats = new LlmProvider().dao().find(
                "SELECT " +
                "  CASE WHEN use_proxy = 1 THEN '使用代理' ELSE '不使用代理' END as proxy_status, " +
                "  COUNT(*) as count, " +
                "  GROUP_CONCAT(name ORDER BY name) as providers " +
                "FROM llm_provider " +
                "WHERE status = 1 " +
                "GROUP BY use_proxy " +
                "ORDER BY use_proxy DESC"
            );
            
            renderJsonData(stats);
            
        } catch (Exception e) {
            LogKit.error("获取代理配置统计失败: " + e.getMessage());
            renderJsonFail("获取统计失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试提供商代理连接
     */
    public void testProxyConnection() {
        try {
            Long providerId = getParaToLong("providerId");
            
            if (providerId == null) {
                renderJsonFail("提供商ID不能为空");
                return;
            }
            
            LlmProvider provider = new LlmProvider().dao().findById(providerId);
            if (provider == null) {
                renderJsonFail("提供商不存在");
                return;
            }
            
            Boolean useProxy = provider.getBoolean("use_proxy");
            if (useProxy == null || !useProxy) {
                renderJsonSuccess("该提供商不使用代理，无需测试");
                return;
            }
            
            String proxyHost = provider.getStr("proxy_host");
            Integer proxyPort = provider.getInt("proxy_port");
            
            if (proxyHost == null || proxyHost.trim().isEmpty() || proxyPort == null || proxyPort <= 0) {
                renderJsonFail("代理配置不完整");
                return;
            }
            
            // 测试代理连接
            long startTime = System.currentTimeMillis();
            boolean connected = testProxyConnection(proxyHost, proxyPort);
            long endTime = System.currentTimeMillis();
            
            Map<String, Object> result = new HashMap<>();
            result.put("providerName", provider.getName());
            result.put("proxyHost", proxyHost);
            result.put("proxyPort", proxyPort);
            result.put("connected", connected);
            result.put("responseTime", (endTime - startTime) + "ms");
            
            if (connected) {
                renderJsonData(result, "代理连接测试成功");
            } else {
                renderJsonData(result, "代理连接测试失败");
            }
            
        } catch (Exception e) {
            LogKit.error("测试代理连接失败: " + e.getMessage());
            renderJsonFail("测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 实际测试代理连接
     */
    private boolean testProxyConnection(String proxyHost, int proxyPort) {
        try {
            // 创建临时的LlmProvider用于测试
            LlmProvider testProvider = new LlmProvider();
            testProvider.set("use_proxy", 1);
            testProvider.set("proxy_host", proxyHost);
            testProvider.set("proxy_port", proxyPort);
            testProvider.set("proxy_type", "HTTP");
            testProvider.setName("test");
            
            // 使用ProxyConfigUtil创建客户端并测试连接
            okhttp3.OkHttpClient client = cn.jbolt.llm.util.ProxyConfigUtil.createHttpClientWithProxy(testProvider);
            
            okhttp3.Request request = new okhttp3.Request.Builder()
                .url("https://www.google.com")
                .head()
                .build();
            
            try (okhttp3.Response response = client.newCall(request).execute()) {
                return response.isSuccessful();
            }
            
        } catch (Exception e) {
            LogKit.warn("代理连接测试异常: " + e.getMessage());
            return false;
        }
    }
}
