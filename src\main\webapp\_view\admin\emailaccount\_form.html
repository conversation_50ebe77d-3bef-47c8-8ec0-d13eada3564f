<form onsubmit="return false;" id="EmailAccount_Form" action="#(action)" method="post">
    #if(emailAccount.id??)
    <input type="hidden" name="emailAccount.id" value="#(emailAccount.id??)"/>
    #end
    <div class="row">
        <div class="col">
            <div class="form-group row">
                <label class="col-sm-2 col-form-label">简称</label>
                <div class="col-10">
                    <input type="text" data-with-clearbtn="true" autocomplete="off" class="form-control"
                           placeholder="请输入简称" maxlength="128" name="emailAccount.nickname"
                           value="#(emailAccount.nickname?? )"/>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-2 col-form-label">用户名</label>
                <div class="col-10">
                    <input type="text" data-with-clearbtn="true" autocomplete="off" class="form-control"
                           placeholder="请输入用户名" maxlength="255" name="emailAccount.username"
                           value="#(emailAccount.username?? )"/>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-2 col-form-label">发件人名称</label>
                <div class="col-10">
                    <input type="text" data-with-clearbtn="true" autocomplete="off" class="form-control"
                           placeholder="请输入发件人名称" maxlength="128" name="emailAccount.senderName"
                           value="#(emailAccount.senderName?? )"/>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-2 col-form-label">密码</label>
                <div class="col-10">
                    <input type="text" data-with-clearbtn="true" autocomplete="off" class="form-control"
                           placeholder="请输入密码" maxlength="255" name="emailAccount.password"
                           value="#(emailAccount.password?? )"/>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-2 col-form-label">imap地址</label>
                <div class="col-10">
                    <input type="text" class="form-control" data-autocomplete autocomplete="off" maxlength="255"
                           placeholder="请选择imap地址"
                           data-url="admin/emailAccount/autocompleteDatas"
                           data-text-attr="imapHost,imapPort"
                           data-value-attr="imapHost,imapPort"
                           data-hidden-input="imapHost_hidden,emailAccountImapPort"
                           value="#(emailAccount.imapHost??)"
                    />
                    <input type="hidden" data-sync-attr="imapHost" autocomplete="off" id="imapHost_hidden" maxLength="255"
                           name="emailAccount.imapHost"
                           value="#(emailAccount.imapHost??)"
                    />
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-2 col-form-label">imap端口</label>
                <div class="col-10">
                    <input type="text" data-sync-attr="imapPort" data-with-clearbtn="true" autocomplete="off" class="form-control"
                           placeholder="请输入imap端口" maxlength="255" id="emailAccountImapPort" name="emailAccount.imapPort"
                           value="#(emailAccount.imapPort?? )"/>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-2 col-form-label">smtp地址</label>
                <div class="col-10">
                    <input type="text" class="form-control" data-autocomplete autocomplete="off" maxlength="255"
                           placeholder="请选择smtp地址"
                           data-url="admin/emailAccount/autocompleteDatas"
                           data-text-attr="smtpHost,smtpPort"
                           data-value-attr="smtpHost,smtpPort"
                           data-hidden-input="smtpHost_hidden,emailAccountSmtpPort"
                           value="#(emailAccount.smtpHost??)"
                    />
                    <input type="hidden" data-sync-attr="smtpHost" autocomplete="off" id="smtpHost_hidden" maxLength="255"
                           name="emailAccount.smtpHost"
                           value="#(emailAccount.smtpHost??)"
                    />
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-2 col-form-label">smtp端口</label>
                <div class="col-10">
                    <input type="text" data-sync-attr="smtpPort" data-with-clearbtn="true" autocomplete="off" class="form-control"
                           placeholder="请输入smtp端口" maxlength="255" id="emailAccountSmtpPort" name="emailAccount.smtpPort"
                           value="#(emailAccount.smtpPort?? )"/>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-sm-2 col-form-label">备注信息</label>
                <div class="col-10">
                    <textarea style="height:100px;" data-with-clearbtn="true" autocomplete="off" class="form-control"
                              placeholder="请输入备注信息" maxlength="255" name="emailAccount.remark">#(emailAccount.remark??)</textarea>
                </div>
            </div>
            <div class="form-group row"
                 data-radio
                 data-name="emailAccount.enable"
                 data-value-attr="sn"
                 data-default="true"
                 data-url="admin/dictionary/options?key=options_enable"
                 data-label="启用/禁用"
                 data-width="col-sm-2,col-10"
                 data-value="#(emailAccount.enable??)"
                 data-rule="radio"
                 data-notnull="false"
                 data-tips="请选择启用/禁用"
                 data-inline="true"
            >
            </div>
            <div class="form-group row"
                 data-radio
                 data-name="emailAccount.valid"
                 data-value-attr="sn"
                 data-default="true"
                 data-url="admin/dictionary/options?key=options_boolean"
                 data-label="有效?"
                 data-width="col-sm-2,col-10"
                 data-value="#(emailAccount.valid??)"
                 data-rule="radio"
                 data-notnull="false"
                 data-tips="请选择有效?"
                 data-inline="true"
            >
            </div>
        </div>
    </div>
</form>
#define js()
<script>
</script>
#include("/_view/_admin/common/_formjs.html",formId="EmailAccount_Form")
#end

