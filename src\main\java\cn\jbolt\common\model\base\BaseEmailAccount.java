package cn.jbolt.common.model.base;
import cn.jbolt.core.model.base.JBoltBaseModel;
import cn.jbolt.core.gen.JBoltField;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.ToStringSerializer;

/**
 * 邮箱账户
 * Generated by <PERSON><PERSON><PERSON>, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseEmailAccount<M extends BaseEmailAccount<M>> extends JBoltBaseModel<M>{
    public static final String DATASOURCE_CONFIG_NAME = "main";
    /**ID*/
    public static final String ID = "id";
    /**简称*/
    public static final String NICKNAME = "nickname";
    /**用户名*/
    public static final String USERNAME = "username";
    /**发件人名称*/
    public static final String SENDER_NAME = "sender_name";
    /**密码*/
    public static final String PASSWORD = "password";
    /**imap地址*/
    public static final String IMAP_HOST = "imap_host";
    /**imap端口*/
    public static final String IMAP_PORT = "imap_port";
    /**smtp地址*/
    public static final String SMTP_HOST = "smtp_host";
    /**smtp端口*/
    public static final String SMTP_PORT = "smtp_port";
    /**有效*/
    public static final String VALID = "valid";
    /**顺序*/
    public static final String SORT_RANK = "sort_rank";
    /**备注信息*/
    public static final String REMARK = "remark";
    /**启用/禁用*/
    public static final String ENABLE = "enable";
    /**监控*/
    public static final String MONITOR = "monitor";
    /**创建时间*/
    public static final String CREATE_TIME = "create_time";
    /**创建人*/
    public static final String CREATE_USER_ID = "create_user_id";
    /**更新时间*/
    public static final String UPDATE_TIME = "update_time";
    /**更新人id*/
    public static final String UPDATE_USER_ID = "update_user_id";
    /**历史邮件是否已完整同步 0未同步 1已同步*/
    public static final String HISTORY_SYNCED = "history_synced";
    /**最后同步时间*/
    public static final String LAST_SYNC_TIME = "last_sync_time";
    /**是否已完成首次同步*/
    public static final String FIRST_SYNC_COMPLETED = "first_sync_completed";
    /**接收模式*/
    public static final String RECEIVE_MODE = "receive_mode";
    /**间隔秒数*/
    public static final String POLLING_INTERVAL = "polling_interval";
    /**是否启用SSL*/
    public static final String SSL = "ssl";
    /**接收,1接收，0不接收*/
    public static final String RECEIVE = "receive";
    /**发送,1发送，0不发送*/
    public static final String SEND = "send";
	/**
	 * ID
	 */
	public M setId(java.lang.Integer id) {
		set("id", id);
		return (M)this;
	}
	
	/**
	 * ID
	 */
	@JBoltField(name="id" ,columnName="id",type="Integer", remark="ID", required=true, maxLength=10, fixed=0, order=1)
	public java.lang.Integer getId() {
		return getInt("id");
	}

	/**
	 * 简称
	 */
	public M setNickname(java.lang.String nickname) {
		set("nickname", nickname);
		return (M)this;
	}
	
	/**
	 * 简称
	 */
	@JBoltField(name="nickname" ,columnName="nickname",type="String", remark="简称", required=false, maxLength=128, fixed=0, order=2)
	public java.lang.String getNickname() {
		return getStr("nickname");
	}

	/**
	 * 用户名
	 */
	public M setUsername(java.lang.String username) {
		set("username", username);
		return (M)this;
	}
	
	/**
	 * 用户名
	 */
	@JBoltField(name="username" ,columnName="username",type="String", remark="用户名", required=false, maxLength=255, fixed=0, order=3)
	public java.lang.String getUsername() {
		return getStr("username");
	}

	/**
	 * 发件人名称
	 */
	public M setSenderName(java.lang.String senderName) {
		set("sender_name", senderName);
		return (M)this;
	}

	/**
	 * 发件人名称
	 */
	@JBoltField(name="senderName" ,columnName="sender_name",type="String", remark="发件人名称", required=false, maxLength=128, fixed=0, order=4)
	public java.lang.String getSenderName() {
		return getStr("sender_name");
	}

	/**
	 * 密码
	 */
	public M setPassword(java.lang.String password) {
		set("password", password);
		return (M)this;
	}
	
	/**
	 * 密码
	 */
	@JBoltField(name="password" ,columnName="password",type="String", remark="密码", required=false, maxLength=255, fixed=0, order=5)
	public java.lang.String getPassword() {
		return getStr("password");
	}

	/**
	 * imap地址
	 */
	public M setImapHost(java.lang.String imapHost) {
		set("imap_host", imapHost);
		return (M)this;
	}

	/**
	 * imap地址
	 */
	@JBoltField(name="imapHost" ,columnName="imap_host",type="String", remark="imap地址", required=false, maxLength=255, fixed=0, order=6)
	public java.lang.String getImapHost() {
		return getStr("imap_host");
	}

	/**
	 * imap端口
	 */
	public M setImapPort(java.lang.String imapPort) {
		set("imap_port", imapPort);
		return (M)this;
	}
	
	/**
	 * imap端口
	 */
	@JBoltField(name="imapPort" ,columnName="imap_port",type="String", remark="imap端口", required=false, maxLength=255, fixed=0, order=7)
	public java.lang.String getImapPort() {
		return getStr("imap_port");
	}

	/**
	 * smtp地址
	 */
	public M setSmtpHost(java.lang.String smtpHost) {
		set("smtp_host", smtpHost);
		return (M)this;
	}

	/**
	 * smtp地址
	 */
	@JBoltField(name="smtpHost" ,columnName="smtp_host",type="String", remark="smtp地址", required=false, maxLength=255, fixed=0, order=8)
	public java.lang.String getSmtpHost() {
		return getStr("smtp_host");
	}

	/**
	 * smtp端口
	 */
	public M setSmtpPort(java.lang.String smtpPort) {
		set("smtp_port", smtpPort);
		return (M)this;
	}

	/**
	 * smtp端口
	 */
	@JBoltField(name="smtpPort" ,columnName="smtp_port",type="String", remark="smtp端口", required=false, maxLength=255, fixed=0, order=9)
	public java.lang.String getSmtpPort() {
		return getStr("smtp_port");
	}

	/**
	 * 有效
	 */
	public M setValid(java.lang.Boolean valid) {
		set("valid", valid);
		return (M)this;
	}
	
	/**
	 * 有效
	 */
	@JBoltField(name="valid" ,columnName="valid",type="Boolean", remark="有效", required=false, maxLength=1, fixed=0, order=10)
	public java.lang.Boolean getValid() {
		return getBoolean("valid");
	}

	/**
	 * 顺序
	 */
	public M setSortRank(java.lang.Integer sortRank) {
		set("sort_rank", sortRank);
		return (M)this;
	}

	/**
	 * 顺序
	 */
	@JBoltField(name="sortRank" ,columnName="sort_rank",type="Integer", remark="顺序", required=false, maxLength=10, fixed=0, order=11)
	public java.lang.Integer getSortRank() {
		return getInt("sort_rank");
	}

	/**
	 * 备注信息
	 */
	public M setRemark(java.lang.String remark) {
		set("remark", remark);
		return (M)this;
	}

	/**
	 * 备注信息
	 */
	@JBoltField(name="remark" ,columnName="remark",type="String", remark="备注信息", required=false, maxLength=255, fixed=0, order=12)
	public java.lang.String getRemark() {
		return getStr("remark");
	}

	/**
	 * 启用/禁用
	 */
	public M setEnable(java.lang.Boolean enable) {
		set("enable", enable);
		return (M)this;
	}
	
	/**
	 * 启用/禁用
	 */
	@JBoltField(name="enable" ,columnName="enable",type="Boolean", remark="启用/禁用", required=false, maxLength=1, fixed=0, order=13)
	public java.lang.Boolean getEnable() {
		return getBoolean("enable");
	}

	/**
	 * 监控
	 */
	public M setMonitor(java.lang.Boolean monitor) {
		set("monitor", monitor);
		return (M)this;
	}

	/**
	 * 监控
	 */
	@JBoltField(name="monitor" ,columnName="monitor",type="Boolean", remark="监控", required=false, maxLength=1, fixed=0, order=14)
	public java.lang.Boolean getMonitor() {
		return getBoolean("monitor");
	}

	/**
	 * 创建时间
	 */
	public M setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
		return (M)this;
	}

	/**
	 * 创建时间
	 */
	@JBoltField(name="createTime" ,columnName="create_time",type="Date", remark="创建时间", required=false, maxLength=19, fixed=0, order=15)
	public java.util.Date getCreateTime() {
		return getDate("create_time");
	}

	/**
	 * 创建人
	 */
	public M setCreateUserId(java.lang.Long createUserId) {
		set("create_user_id", createUserId);
		return (M)this;
	}
	
	/**
	 * 创建人
	 */
	@JBoltField(name="createUserId" ,columnName="create_user_id",type="Long", remark="创建人", required=false, maxLength=19, fixed=0, order=16)
	@JSONField(serializeUsing= ToStringSerializer.class)
	public java.lang.Long getCreateUserId() {
		return getLong("create_user_id");
	}

	/**
	 * 更新时间
	 */
	public M setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
		return (M)this;
	}

	/**
	 * 更新时间
	 */
	@JBoltField(name="updateTime" ,columnName="update_time",type="Date", remark="更新时间", required=false, maxLength=19, fixed=0, order=17)
	public java.util.Date getUpdateTime() {
		return getDate("update_time");
	}

	/**
	 * 更新人id
	 */
	public M setUpdateUserId(java.lang.Long updateUserId) {
		set("update_user_id", updateUserId);
		return (M)this;
	}

	/**
	 * 更新人id
	 */
	@JBoltField(name="updateUserId" ,columnName="update_user_id",type="Long", remark="更新人id", required=false, maxLength=19, fixed=0, order=18)
	@JSONField(serializeUsing= ToStringSerializer.class)
	public java.lang.Long getUpdateUserId() {
		return getLong("update_user_id");
	}

	/**
	 * 历史邮件是否已完整同步 0未同步 1已同步
	 */
	public M setHistorySynced(java.lang.Boolean historySynced) {
		set("history_synced", historySynced);
		return (M)this;
	}
	
	/**
	 * 历史邮件是否已完整同步 0未同步 1已同步
	 */
	@JBoltField(name="historySynced" ,columnName="history_synced",type="Boolean", remark="历史邮件是否已完整同步 0未同步 1已同步", required=false, maxLength=1, fixed=0, order=19)
	public java.lang.Boolean getHistorySynced() {
		return getBoolean("history_synced");
	}

	/**
	 * 最后同步时间
	 */
	public M setLastSyncTime(java.util.Date lastSyncTime) {
		set("last_sync_time", lastSyncTime);
		return (M)this;
	}

	/**
	 * 最后同步时间
	 */
	@JBoltField(name="lastSyncTime" ,columnName="last_sync_time",type="Date", remark="最后同步时间", required=false, maxLength=19, fixed=0, order=20)
	public java.util.Date getLastSyncTime() {
		return getDate("last_sync_time");
	}

	/**
	 * 是否已完成首次同步
	 */
	public M setFirstSyncCompleted(java.lang.Boolean firstSyncCompleted) {
		set("first_sync_completed", firstSyncCompleted);
		return (M)this;
	}

	/**
	 * 是否已完成首次同步
	 */
	@JBoltField(name="firstSyncCompleted" ,columnName="first_sync_completed",type="Boolean", remark="是否已完成首次同步", required=false, maxLength=1, fixed=0, order=21)
	public java.lang.Boolean getFirstSyncCompleted() {
		return getBoolean("first_sync_completed");
	}

	/**
	 * 接收模式
	 */
	public M setReceiveMode(java.lang.Integer receiveMode) {
		set("receive_mode", receiveMode);
		return (M)this;
	}
	
	/**
	 * 接收模式
	 */
	@JBoltField(name="receiveMode" ,columnName="receive_mode",type="Integer", remark="接收模式", required=false, maxLength=10, fixed=0, order=22)
	public java.lang.Integer getReceiveMode() {
		return getInt("receive_mode");
	}

	/**
	 * 间隔秒数
	 */
	public M setPollingInterval(java.lang.Integer pollingInterval) {
		set("polling_interval", pollingInterval);
		return (M)this;
	}

	/**
	 * 间隔秒数
	 */
	@JBoltField(name="pollingInterval" ,columnName="polling_interval",type="Integer", remark="间隔秒数", required=false, maxLength=10, fixed=0, order=23)
	public java.lang.Integer getPollingInterval() {
		return getInt("polling_interval");
	}

	/**
	 * 是否启用SSL
	 */
	public M setSsl(java.lang.Boolean ssl) {
		set("ssl", ssl);
		return (M)this;
	}

	/**
	 * 是否启用SSL
	 */
	@JBoltField(name="ssl" ,columnName="ssl",type="Boolean", remark="是否启用SSL", required=false, maxLength=1, fixed=0, order=24)
	public java.lang.Boolean getSsl() {
		return getBoolean("ssl");
	}

	/**
	 * 接收,1接收，0不接收
	 */
	public M setReceive(java.lang.Integer receive) {
		set("receive", receive);
		return (M)this;
	}

	/**
	 * 接收,1接收，0不接收
	 */
	@JBoltField(name="receive" ,columnName="receive",type="Integer", remark="接收,1接收，0不接收", required=false, maxLength=3, fixed=0, order=25)
	public java.lang.Integer getReceive() {
		return getInt("receive");
	}

	/**
	 * 发送,1发送，0不发送
	 */
	public M setSend(java.lang.Integer send) {
		set("send", send);
		return (M)this;
	}

	/**
	 * 发送,1发送，0不发送
	 */
	@JBoltField(name="send" ,columnName="send",type="Integer", remark="发送,1发送，0不发送", required=false, maxLength=3, fixed=0, order=26)
	public java.lang.Integer getSend() {
		return getInt("send");
	}

}

