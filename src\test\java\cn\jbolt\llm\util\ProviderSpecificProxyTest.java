package cn.jbolt.llm.util;

import cn.jbolt.common.model.LlmProvider;
import cn.jbolt.mail.gpt.InitEnv;
import org.junit.Before;
import org.junit.Test;

import java.util.List;

/**
 * 提供商特定代理配置测试
 */
public class ProviderSpecificProxyTest {
    
    @Before
    public void setUp() {
        InitEnv.initEnvironment();
    }
    
    /**
     * 测试1：检查提供商代理配置
     */
    @Test
    public void testProviderProxyConfig() {
        System.out.println("=== 测试1：检查提供商代理配置 ===");
        
        try {
            List<LlmProvider> providers = new LlmProvider().dao().find(
                "SELECT * FROM llm_provider WHERE status = 1 ORDER BY priority"
            );
            
            System.out.println("启用的提供商数量: " + providers.size());
            
            for (LlmProvider provider : providers) {
                Boolean useProxy = provider.getBoolean("use_proxy");
                String proxyHost = provider.getStr("proxy_host");
                Integer proxyPort = provider.getInt("proxy_port");
                String proxyType = provider.getStr("proxy_type");
                
                System.out.println("\n提供商: " + provider.getName());
                System.out.println("  API类型: " + provider.getApiType());
                System.out.println("  使用代理: " + (useProxy != null && useProxy ? "是" : "否"));
                
                if (useProxy != null && useProxy) {
                    System.out.println("  代理配置: " + proxyHost + ":" + proxyPort + " (" + proxyType + ")");
                    
                    if (proxyHost == null || proxyHost.trim().isEmpty() || proxyPort == null || proxyPort <= 0) {
                        System.out.println("  ⚠ 代理配置不完整");
                    } else {
                        System.out.println("  ✓ 代理配置完整");
                    }
                } else {
                    System.out.println("  ✓ 直连访问");
                }
            }
            
        } catch (Exception e) {
            System.err.println("✗ 检查提供商代理配置失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("测试1完成\n");
    }
    
    /**
     * 测试2：设置国外提供商使用代理
     */
    @Test
    public void testSetForeignProvidersProxy() {
        System.out.println("=== 测试2：设置国外提供商使用代理 ===");
        
        try {
            String[] foreignProviders = {"gemini", "openai", "claude"};
            String proxyHost = "127.0.0.1";
            int proxyPort = 7890;
            
            for (String providerName : foreignProviders) {
                LlmProvider provider = new LlmProvider().dao().findFirst(
                    "SELECT * FROM llm_provider WHERE name = ?", providerName
                );
                
                if (provider != null) {
                    provider.set("use_proxy", 1);
                    provider.set("proxy_host", proxyHost);
                    provider.set("proxy_port", proxyPort);
                    provider.set("proxy_type", "HTTP");
                    provider.set("update_time", new java.util.Date());
                    
                    boolean success = provider.update();
                    
                    if (success) {
                        System.out.println("✓ " + providerName + " 代理配置设置成功: " + proxyHost + ":" + proxyPort);
                    } else {
                        System.out.println("✗ " + providerName + " 代理配置设置失败");
                    }
                } else {
                    System.out.println("⚠ " + providerName + " 提供商不存在");
                }
            }
            
        } catch (Exception e) {
            System.err.println("✗ 设置国外提供商代理失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("测试2完成\n");
    }
    
    /**
     * 测试3：设置国内提供商不使用代理
     */
    @Test
    public void testSetDomesticProvidersNoProxy() {
        System.out.println("=== 测试3：设置国内提供商不使用代理 ===");
        
        try {
            String[] domesticProviders = {"Kimi Moonshot", "kimi", "moonshot", "qwen", "tongyi", "ernie", "wenxin", "zhipu", "glm"};
            
            for (String providerName : domesticProviders) {
                LlmProvider provider = new LlmProvider().dao().findFirst(
                    "SELECT * FROM llm_provider WHERE name = ?", providerName
                );
                
                if (provider != null) {
                    provider.set("use_proxy", 0);
                    provider.set("proxy_host", "");
                    provider.set("proxy_port", 0);
                    provider.set("proxy_type", "HTTP");
                    provider.set("update_time", new java.util.Date());
                    
                    boolean success = provider.update();
                    
                    if (success) {
                        System.out.println("✓ " + providerName + " 设置为不使用代理");
                    } else {
                        System.out.println("✗ " + providerName + " 代理配置设置失败");
                    }
                } else {
                    System.out.println("⚠ " + providerName + " 提供商不存在");
                }
            }
            
        } catch (Exception e) {
            System.err.println("✗ 设置国内提供商代理失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("测试3完成\n");
    }
    
    /**
     * 测试4：创建提供商特定的HTTP客户端
     */
    @Test
    public void testCreateProviderSpecificClient() {
        System.out.println("=== 测试4：创建提供商特定的HTTP客户端 ===");
        
        try {
            List<LlmProvider> providers = new LlmProvider().dao().find(
                "SELECT * FROM llm_provider WHERE status = 1 ORDER BY name"
            );
            
            for (LlmProvider provider : providers) {
                try {
                    okhttp3.OkHttpClient client = ProxyConfigUtil.createHttpClientWithProxy(provider);
                    
                    System.out.println("\n提供商: " + provider.getName());
                    System.out.println("  HTTP客户端创建: ✓ 成功");
                    System.out.println("  连接超时: " + client.connectTimeoutMillis() + "ms");
                    System.out.println("  读取超时: " + client.readTimeoutMillis() + "ms");
                    
                    if (client.proxy() != null) {
                        System.out.println("  代理设置: " + client.proxy().address());
                    } else {
                        System.out.println("  代理设置: 直连");
                    }
                    
                } catch (Exception e) {
                    System.out.println("\n提供商: " + provider.getName());
                    System.out.println("  HTTP客户端创建: ✗ 失败 - " + e.getMessage());
                }
            }
            
        } catch (Exception e) {
            System.err.println("✗ 创建提供商特定HTTP客户端失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("测试4完成\n");
    }
    
    /**
     * 测试5：代理配置统计
     */
    @Test
    public void testProxyConfigStats() {
        System.out.println("=== 测试5：代理配置统计 ===");
        
        try {
            List<LlmProvider> allProviders = new LlmProvider().dao().find(
                "SELECT * FROM llm_provider WHERE status = 1"
            );
            
            int useProxyCount = 0;
            int noProxyCount = 0;
            
            System.out.println("提供商代理配置统计:");
            
            for (LlmProvider provider : allProviders) {
                Boolean useProxy = provider.getBoolean("use_proxy");
                if (useProxy != null && useProxy) {
                    useProxyCount++;
                    System.out.println("  ✓ " + provider.getName() + " - 使用代理");
                } else {
                    noProxyCount++;
                    System.out.println("  ○ " + provider.getName() + " - 直连");
                }
            }
            
            System.out.println("\n统计结果:");
            System.out.println("  总提供商数: " + allProviders.size());
            System.out.println("  使用代理: " + useProxyCount + " 个");
            System.out.println("  直连访问: " + noProxyCount + " 个");
            
        } catch (Exception e) {
            System.err.println("✗ 代理配置统计失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("测试5完成\n");
    }
    
    /**
     * 运行所有测试
     */
    @Test
    public void runAllTests() {
        System.out.println("========================================");
        System.out.println("提供商特定代理配置测试");
        System.out.println("========================================");
        
        testProviderProxyConfig();
        testSetForeignProvidersProxy();
        testSetDomesticProvidersNoProxy();
        testCreateProviderSpecificClient();
        testProxyConfigStats();
        
        System.out.println("========================================");
        System.out.println("所有测试完成");
        System.out.println("========================================");
        
        System.out.println("\n配置建议:");
        System.out.println("1. 国外模型（Gemini、OpenAI、Claude）设置使用代理");
        System.out.println("2. 国内模型（Kimi、通义千问、文心一言等）设置不使用代理");
        System.out.println("3. 代理配置: 127.0.0.1:7890 (Clash) 或 127.0.0.1:1080 (其他)");
        System.out.println("4. 执行 sql/add_proxy_config_to_llm_provider.sql 添加代理字段");
        
        System.out.println("\n使用优势:");
        System.out.println("- 精确控制：每个提供商独立配置代理");
        System.out.println("- 性能优化：国内模型直连，避免不必要的代理延迟");
        System.out.println("- 灵活管理：可以单独调整每个提供商的网络配置");
        System.out.println("- 故障隔离：某个代理失败不影响其他提供商");
    }
}
